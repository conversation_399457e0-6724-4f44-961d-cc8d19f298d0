const mongoose = require("mongoose");
require("dotenv").config();

// Check what PDF URLs are stored in the database
async function checkPDFUrls() {
	try {
		console.log("🔍 Checking PDF URLs in database...");

		// Connect to MongoDB
		await mongoose.connect(process.env.MONGODB_URI);
		console.log("✅ Connected to MongoDB");

		// Define the donation schema (simplified)
		const donationSchema = new mongoose.Schema({}, { strict: false });
		const Donation = mongoose.model("Donation", donationSchema);

		// Find donations with PDF receipt URLs
		const donationsWithPDF = await Donation.find({
			pdfReceiptUrl: { $exists: true, $ne: null, $ne: "" },
		})
			.select("_id status pdfReceiptUrl confirmationDate createdAt")
			.limit(10);

		console.log(
			`📋 Found ${donationsWithPDF.length} donations with PDF receipts:`
		);

		donationsWithPDF.forEach((donation, index) => {
			console.log(`\n${index + 1}. Donation ID: ${donation._id}`);
			console.log(`   Status: ${donation.status}`);
			console.log(`   PDF URL: ${donation.pdfReceiptUrl}`);
			console.log(`   Confirmation Date: ${donation.confirmationDate}`);
			console.log(`   Created: ${donation.createdAt}`);

			// Check if URL is Cloudinary
			if (donation.pdfReceiptUrl.includes("cloudinary.com")) {
				console.log("   ✅ Cloudinary URL");
			} else if (donation.pdfReceiptUrl.startsWith("/uploads/")) {
				console.log("   ❌ Local file path (should be Cloudinary)");
			} else if (donation.pdfReceiptUrl.startsWith("http")) {
				console.log("   ⚠️  External URL (not Cloudinary)");
			} else {
				console.log("   ❓ Unknown URL format");
			}
		});

		// Check for donations with CONFIRMED status but no PDF
		const confirmedWithoutPDF = await Donation.find({
			status: "CONFIRMED",
			$or: [
				{ pdfReceiptUrl: { $exists: false } },
				{ pdfReceiptUrl: null },
				{ pdfReceiptUrl: "" },
			],
		})
			.select("_id status confirmationDate")
			.limit(5);

		if (confirmedWithoutPDF.length > 0) {
			console.log(
				`\n⚠️  Found ${confirmedWithoutPDF.length} CONFIRMED donations without PDF receipts:`
			);
			confirmedWithoutPDF.forEach((donation, index) => {
				console.log(
					`${index + 1}. ID: ${donation._id}, Confirmed: ${
						donation.confirmationDate
					}`
				);
			});
		}
	} catch (error) {
		console.error("❌ Error:", error);
	} finally {
		await mongoose.disconnect();
		console.log("\n🔌 Disconnected from MongoDB");
	}
}

// Run the check
checkPDFUrls();
