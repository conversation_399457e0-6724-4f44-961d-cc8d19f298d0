const mongoose = require('mongoose');
require('dotenv').config({ path: './Charity_donation_Backend/.env' });

// Test PDF generation and Cloudinary upload
async function testPDFGeneration() {
    try {
        console.log('🧪 Testing PDF generation and Cloudinary upload...');
        
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Connected to MongoDB');
        
        // Import the PDF generator
        const { generateDonationReceipt } = require('./Charity_donation_Backend/src/utils/pdfGenerator.ts');
        
        // Test data
        const testDonationData = {
            donationId: 'test-donation-123',
            donorName: 'Test Donor',
            donorEmail: '<EMAIL>',
            organizationName: 'Test Organization',
            organizationEmail: '<EMAIL>',
            amount: 1000,
            type: 'MONEY',
            description: 'Test monetary donation',
            receivedDate: new Date(),
            cause: 'Test Cause'
        };
        
        console.log('📄 Generating PDF receipt...');
        const pdfUrl = await generateDonationReceipt(testDonationData);
        
        console.log('✅ PDF generated successfully!');
        console.log('📋 PDF URL:', pdfUrl);
        
        // Verify the URL is a Cloudinary URL
        if (pdfUrl.includes('cloudinary.com')) {
            console.log('✅ PDF uploaded to Cloudinary successfully');
        } else {
            console.log('❌ PDF URL is not a Cloudinary URL');
        }
        
        // Test the URL accessibility
        const fetch = require('node-fetch');
        try {
            const response = await fetch(pdfUrl, { method: 'HEAD' });
            if (response.ok) {
                console.log('✅ PDF URL is accessible');
            } else {
                console.log('❌ PDF URL is not accessible:', response.status);
            }
        } catch (fetchError) {
            console.log('❌ Error checking PDF URL accessibility:', fetchError.message);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

// Run the test
testPDFGeneration();
