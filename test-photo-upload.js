const fs = require("fs");
const FormData = require("form-data");
// Use built-in fetch for Node.js 18+

// Test photo upload functionality
async function testPhotoUpload() {
	try {
		console.log("🧪 Testing photo upload functionality...");

		// Create a simple test image buffer (1x1 pixel PNG)
		const testImageBuffer = Buffer.from([
			0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
			0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
			0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xde, 0x00, 0x00, 0x00,
			0x0c, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
			0xff, 0xff, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xe2, 0x21, 0xbc, 0x33,
			0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
		]);

		// Create FormData
		const formData = new FormData();
		formData.append("photo", testImageBuffer, {
			filename: "test-image.png",
			contentType: "image/png",
		});

		// Test the Cloudinary middleware directly
		console.log("📤 Testing Cloudinary upload middleware...");

		const response = await fetch(
			"http://localhost:8080/api/donations/test-donation-id/received",
			{
				method: "PATCH",
				body: formData,
				headers: {
					Authorization: "Bearer test-token",
					...formData.getHeaders(),
				},
			}
		);

		const result = await response.text();
		console.log("📋 Response status:", response.status);
		console.log("📋 Response body:", result);

		if (response.status === 401) {
			console.log(
				"✅ Authentication check working (expected 401 for test token)"
			);
		} else if (
			response.status === 400 &&
			result.includes("multipart/form-data")
		) {
			console.log("✅ Content-type validation working");
		} else if (
			response.status === 400 &&
			result.includes("Photo upload to cloud storage failed")
		) {
			console.log("❌ Photo upload middleware issue detected");
		} else {
			console.log("📋 Unexpected response - check logs");
		}
	} catch (error) {
		console.error("❌ Test failed:", error.message);
	}
}

// Run the test
testPhotoUpload();
